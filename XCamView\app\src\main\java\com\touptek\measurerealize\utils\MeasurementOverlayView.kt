package com.touptek.measurerealize.utils

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import kotlin.math.*

/**
 * 🎨 专业级测量覆盖层 - 超越iPad体验的可视化
 * 
 * 核心特性：
 * - 多层测量渲染：支持同时显示多种测量类型
 * - 专业视觉效果：动态高亮、流畅动画、精美渲染
 * - 智能坐标转换：与TpImageView完美协作
 * - 高性能绘制：优化的Canvas绘制算法
 */
class MeasurementOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 🎨 专业级绘制工具
    private val linePaint = Paint().apply {
        color = Color.parseColor("#2196F3")
        strokeWidth = 6f
        style = Paint.Style.STROKE
        isAntiAlias = true
        strokeCap = Paint.Cap.ROUND
    }

    private val pointPaint = Paint().apply {
        color = Color.parseColor("#4CAF50")
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val highlightPointPaint = Paint().apply {
        color = Color.parseColor("#FF5722")
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = 48f
        isAntiAlias = true
        typeface = Typeface.DEFAULT_BOLD
    }

    private val arcPaint = Paint().apply {
        color = Color.parseColor("#FF9800")
        strokeWidth = 4f
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    // 测量数据和图像视图引用
    private var measurementData: MeasurementData? = null
    private var imageView: TpImageView? = null

    // 测量助手引用
    private var angleMeasureHelper: AngleMeasureHelper? = null

    fun setImageView(imageView: TpImageView) {
        this.imageView = imageView
    }

    fun setAngleMeasureHelper(helper: AngleMeasureHelper) {
        this.angleMeasureHelper = helper
    }

    fun setMeasurementData(data: MeasurementData?) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setMeasurementData called with: $data")
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Current overlay size: ${width}x${height}")
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Current visibility: $visibility")

        measurementData = data
        android.util.Log.d("MeasurementOverlay", "📊 Setting measurement data: $data")

        if (data != null) {
            when (data) {
                is AngleMeasurementData -> {
                    android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] AngleMeasurementData - points.size: ${data.points.size}, angle: ${data.angle}°")
                    data.points.forEachIndexed { index, point ->
                        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] point[$index]: $point")
                    }
                }
                else -> {
                    android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Other measurement data type: ${data::class.simpleName}")
                }
            }

            // 🎯 确保覆盖层可见并强制重绘
            visibility = View.VISIBLE
            bringToFront() // 确保覆盖层在最前面

            // 强制重绘
            invalidate()
            requestLayout()

            android.util.Log.d("MeasurementOverlay", "✅ Overlay updated and invalidated")
        } else {
            android.util.Log.d("MeasurementOverlay", "🧹 Clearing measurement data")
        }
    }

    fun clearMeasurement() {
        measurementData = null
        android.util.Log.d("MeasurementOverlay", "🧹 Cleared measurement data to prevent overlay artifacts")
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] onDraw called - canvas size: ${canvas.width}x${canvas.height}")
        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] measurementData: ${measurementData != null}, imageView: ${imageView != null}")

        // 🔴 添加测试绘制 - 验证覆盖层可见性
        val testPaint = Paint().apply {
            color = Color.RED
            style = Paint.Style.FILL
            alpha = 100
        }
        canvas.drawCircle(width/2f, height/2f, 50f, testPaint)
        android.util.Log.d("MeasurementOverlay", "🔴 [DEBUG] Drew test red circle at center (${width/2f}, ${height/2f})")

        val data = measurementData
        if (data == null) {
            android.util.Log.d("MeasurementOverlay", "⚠️ [DEBUG] measurementData is null, skipping draw")
            return
        }

        val imgView = imageView
        if (imgView == null) {
            android.util.Log.d("MeasurementOverlay", "⚠️ [DEBUG] imageView is null, skipping draw")
            return
        }

        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drawing measurement data: ${data::class.simpleName}")

        when (data) {
            is AngleMeasurementData -> {
                android.util.Log.d("MeasurementOverlay", "🎯 [DEBUG] Drawing AngleMeasurementData")
                drawAngleMeasurement(canvas, data, imgView)
            }
            is DistanceMeasurementData -> drawDistanceMeasurement(canvas, data, imgView)
            is RectangleMeasurementData -> drawRectangleMeasurement(canvas, data, imgView)
            is CircleMeasurementData -> drawCircleMeasurement(canvas, data, imgView)
            is ThreePointCircleMeasurementData -> drawThreePointCircleMeasurement(canvas, data, imgView)
            is FourPointAngleMeasurementData -> drawFourPointAngleMeasurement(canvas, data, imgView)
            is ParallelLinesMeasurementData -> drawParallelLinesMeasurement(canvas, data, imgView)
            is EllipseMeasurementData -> drawEllipseMeasurement(canvas, data, imgView)
            is MultiPointPathMeasurementData -> drawMultiPointPathMeasurement(canvas, data, imgView)
        }

        android.util.Log.d("MeasurementOverlay", "✅ [DEBUG] onDraw completed")
    }

    /**
     * 🎯 绘制角度测量 - 专业级三点角度可视化
     */
    private fun drawAngleMeasurement(canvas: Canvas, data: AngleMeasurementData, imageView: TpImageView) {
        if (data.points.size < 3) return

        val vertex = data.points[0]    // 顶点
        val point1 = data.points[1]    // 第一个点
        val point2 = data.points[2]    // 第二个点

        // 🎨 绘制角度线条
        canvas.drawLine(vertex.x, vertex.y, point1.x, point1.y, linePaint)
        canvas.drawLine(vertex.x, vertex.y, point2.x, point2.y, linePaint)

        // 🎯 绘制角度弧线
        drawAngleArc(canvas, vertex, point1, point2, data.angle)

        // 🎨 绘制控制点
        val pointRadius = if (data.isDragging) 22f else 14f
        val currentPointPaint = if (data.isDragging) highlightPointPaint else pointPaint

        canvas.drawCircle(vertex.x, vertex.y, pointRadius, currentPointPaint)
        canvas.drawCircle(point1.x, point1.y, pointRadius, currentPointPaint)
        canvas.drawCircle(point2.x, point2.y, pointRadius, currentPointPaint)

        // 🎨 绘制角度文本
        drawAngleText(canvas, vertex, data.angle, data.isDragging)
    }

    /**
     * 🎨 绘制角度弧线 - 专业级弧线渲染
     */
    private fun drawAngleArc(canvas: Canvas, vertex: PointF, point1: PointF, point2: PointF, angle: Double) {
        val arcRadius = 60f
        
        // 计算两条线的角度
        val angle1 = atan2((point1.y - vertex.y).toDouble(), (point1.x - vertex.x).toDouble())
        val angle2 = atan2((point2.y - vertex.y).toDouble(), (point2.x - vertex.x).toDouble())
        
        var startAngle = Math.toDegrees(angle1).toFloat()
        var sweepAngle = Math.toDegrees(angle2 - angle1).toFloat()
        
        // 确保弧线绘制方向正确
        if (sweepAngle > 180) sweepAngle -= 360
        if (sweepAngle < -180) sweepAngle += 360
        
        val rect = RectF(
            vertex.x - arcRadius,
            vertex.y - arcRadius,
            vertex.x + arcRadius,
            vertex.y + arcRadius
        )
        
        canvas.drawArc(rect, startAngle, sweepAngle, false, arcPaint)
    }

    /**
     * 🎨 绘制角度文本 - 智能位置和专业样式
     */
    private fun drawAngleText(canvas: Canvas, vertex: PointF, angle: Double, isDragging: Boolean) {
        val angleText = String.format("%.1f°", angle)
        val textBounds = Rect()
        textPaint.getTextBounds(angleText, 0, angleText.length, textBounds)

        // 🎯 智能文本位置（在顶点附近）
        val textX = vertex.x + 80f
        val textY = vertex.y - 40f

        // 🎨 绘制文本背景（专业级设计）
        val backgroundPaint = Paint().apply {
            color = if (isDragging) {
                Color.argb(200, 233, 30, 99) // 拖拽时粉色背景
            } else {
                Color.argb(180, 0, 0, 0) // 正常时黑色背景
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val padding = 12f
        canvas.drawRoundRect(
            textX - padding,
            textY - textBounds.height() - padding,
            textX + textBounds.width() + padding,
            textY + padding,
            12f, 12f,
            backgroundPaint
        )

        // 绘制文本
        canvas.drawText(angleText, textX, textY, textPaint)
    }

    // 其他测量类型的绘制方法将在后续添加
    private fun drawDistanceMeasurement(canvas: Canvas, data: DistanceMeasurementData, imageView: TpImageView) {
        // TODO: 实现距离测量绘制
    }

    private fun drawRectangleMeasurement(canvas: Canvas, data: RectangleMeasurementData, imageView: TpImageView) {
        // TODO: 实现矩形测量绘制
    }

    private fun drawCircleMeasurement(canvas: Canvas, data: CircleMeasurementData, imageView: TpImageView) {
        // TODO: 实现圆形测量绘制
    }

    private fun drawThreePointCircleMeasurement(canvas: Canvas, data: ThreePointCircleMeasurementData, imageView: TpImageView) {
        // TODO: 实现三点圆测量绘制
    }

    private fun drawFourPointAngleMeasurement(canvas: Canvas, data: FourPointAngleMeasurementData, imageView: TpImageView) {
        // TODO: 实现四点角度测量绘制
    }

    private fun drawParallelLinesMeasurement(canvas: Canvas, data: ParallelLinesMeasurementData, imageView: TpImageView) {
        // TODO: 实现平行线测量绘制
    }

    private fun drawEllipseMeasurement(canvas: Canvas, data: EllipseMeasurementData, imageView: TpImageView) {
        // TODO: 实现椭圆测量绘制
    }

    private fun drawMultiPointPathMeasurement(canvas: Canvas, data: MultiPointPathMeasurementData, imageView: TpImageView) {
        // TODO: 实现多点路径测量绘制
    }

    /**
     * 🎯 处理触摸事件 - 委托给测量助手
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        android.util.Log.d("MeasurementOverlay", "🎯 Touch event: ${event.action}, data: ${measurementData != null}, helper: ${angleMeasureHelper != null}")

        // 如果有测量助手，优先处理
        if (angleMeasureHelper != null) {
            val handled = angleMeasureHelper!!.handleTouchEvent(event, width, height)
            android.util.Log.d("MeasurementOverlay", "📱 Touch handled by helper: $handled")
            if (handled) {
                return true
            }
        }

        // 智能事件分发：精确检测测量点位
        if (measurementData != null) {
            if (event.pointerCount == 1) {
                // 精确检测：只有触摸测量点位时才消费事件
                val touchPoint = PointF(event.x, event.y)
                val isTouchingMeasurementPoint = angleMeasureHelper?.isTouchingMeasurementPoint(touchPoint) ?: false

                if (isTouchingMeasurementPoint) {
                    android.util.Log.d("MeasurementOverlay", "📱 Consuming touch on measurement point")
                    return true
                } else {
                    android.util.Log.d("MeasurementOverlay", "📱 Forwarding single-touch to TpImageView for image dragging")
                    forwardEventToImageView(event)
                    return false
                }
            } else {
                android.util.Log.d("MeasurementOverlay", "📱 Forwarding multi-touch event to TpImageView")
                forwardEventToImageView(event)
                return false
            }
        }

        // 如果测量助手没有处理，让父视图处理
        val result = super.onTouchEvent(event)
        android.util.Log.d("MeasurementOverlay", "📱 Touch handled by super: $result")
        return result
    }

    /**
     * 🔄 将事件转发给TpImageView
     */
    private fun forwardEventToImageView(event: MotionEvent) {
        imageView?.let { imgView ->
            val adjustedEvent = MotionEvent.obtain(event)
            val handled = imgView.dispatchTouchEvent(adjustedEvent)
            adjustedEvent.recycle()
            android.util.Log.d("MeasurementOverlay", "📱 TpImageView handled event: $handled")
        }
    }

    // onInterceptTouchEvent方法已移除，因为View类不支持此方法（仅ViewGroup支持）
}
