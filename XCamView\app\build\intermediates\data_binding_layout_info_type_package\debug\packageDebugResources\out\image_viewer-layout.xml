<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="image_viewer" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\image_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/image_viewer_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="51" endOffset="13"/></Target><Target id="@+id/image_view" view="com.touptek.measurerealize.TpImageView"><Expressions/><location startLine="6" startOffset="4" endLine="10" endOffset="39"/></Target><Target id="@+id/measurement_overlay" view="com.touptek.measurerealize.utils.MeasurementOverlayView"><Expressions/><location startLine="13" startOffset="4" endLine="20" endOffset="35"/></Target><Target id="@+id/button_panel" view="LinearLayout"><Expressions/><location startLine="23" startOffset="4" endLine="50" endOffset="18"/></Target><Target id="@+id/btn_previous" view="Button"><Expressions/><location startLine="33" startOffset="8" endLine="37" endOffset="32"/></Target><Target id="@+id/btn_next" view="Button"><Expressions/><location startLine="39" startOffset="8" endLine="43" endOffset="32"/></Target><Target id="@+id/btn_back" view="Button"><Expressions/><location startLine="45" startOffset="8" endLine="49" endOffset="31"/></Target></Targets></Layout>