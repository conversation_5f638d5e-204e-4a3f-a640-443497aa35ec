package com.touptek.xcamview.activity.browse.imagemanagement

import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.databinding.ImageViewerBinding
import com.touptek.video.internal.TpImageLoader
import java.util.ArrayList
import com.touptek.xcamview.activity.measurement.TpMeasurementDialogFragment
import com.touptek.measurerealize.MeasurementManager
import android.util.Log

class TpImageDecodeDialogFragment : DialogFragment() {
    private lateinit var binding: ImageViewerBinding
    private lateinit var imagePaths: List<String>
    private var currentPosition = 0

    // 测量工具栏相关变量
    private var measurementDialogFragment: TpMeasurementDialogFragment? = null

    // 角度测量相关变量 - 使用封装的MeasurementManager
    private lateinit var measurementManager: MeasurementManager
    private var isMeasuringAngle = false
    private var currentBitmap: Bitmap? = null
    
    // 界面状态枚举
    private enum class UIState {
        BOTTOM_VISIBLE,    // 底部按钮可见
        TOP_VISIBLE,       // 顶部测量工具栏可见
        ALL_HIDDEN         // 全部隐藏
    }
    
    private var currentUIState = UIState.BOTTOM_VISIBLE

    companion object {
        private const val ARG_IMAGE_PATHS = "image_paths"
        private const val ARG_CURRENT_POSITION = "current_position"

        fun newInstance(imagePaths: List<String>, currentPosition: Int): TpImageDecodeDialogFragment {
            val fragment = TpImageDecodeDialogFragment()
            val args = Bundle().apply {
                putStringArrayList(ARG_IMAGE_PATHS, ArrayList(imagePaths))
                putInt(ARG_CURRENT_POSITION, currentPosition)
            }
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ImageViewerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        imagePaths = arguments?.getStringArrayList(ARG_IMAGE_PATHS) ?: run {
            dismiss()
            return
        }
        currentPosition = arguments?.getInt(ARG_CURRENT_POSITION, 0) ?: 0

        initializeMeasurement()
        loadCurrentImage()
        setupClickListeners()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog)
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.let { window ->
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        }
    }

    private fun setupClickListeners() {
        // 完全移除 OnClickListener，避免与 TpImageView 内部手势冲突
        // binding.imageView.setOnClickListener { toggleButtons() }
        
        // 只使用 TpImageView 的单击事件处理
        setupImageViewClickHandler()
        
        binding.btnPrevious.setOnClickListener { showPreviousImage() }
        binding.btnNext.setOnClickListener { showNextImage() }
        binding.btnBack.setOnClickListener { dismiss() }
    }

    private fun setupImageViewClickHandler() {
        // 初始化时启用单击监听器
        enableSingleTapListener()
    }

    private fun loadCurrentImage() {
        if (currentPosition in imagePaths.indices) {
            TpImageLoader.loadFullImage(imagePaths[currentPosition], binding.imageView)
            updateButtonStates()
            updateCurrentBitmap()
        }
    }

    private fun showPreviousImage() {
        if (currentPosition > 0) {
            currentPosition--
            loadCurrentImage()
        }
    }

    private fun showNextImage() {
        if (currentPosition < imagePaths.size - 1) {
            currentPosition++
            loadCurrentImage()
        }
    }

    private fun updateButtonStates() {
        binding.btnPrevious.isEnabled = currentPosition > 0
        binding.btnNext.isEnabled = currentPosition < imagePaths.size - 1
    }

    private fun cycleUIState() {
        // 🔒 测量模式下完全锁定UI状态，防止工具栏消失
        if (isMeasuringAngle) {
            Log.d("ImageDialog", "🔒 UI state completely locked during measurement mode - cycleUIState blocked")
            Log.d("ImageDialog", "🛡️ Measurement toolbar protection active - no UI state changes allowed")
            return
        }

        when (currentUIState) {
            UIState.BOTTOM_VISIBLE -> {
                // 从底部按钮 → 顶部测量工具栏
                hideBottomButtons()
                showMeasurementToolbar()
                currentUIState = UIState.TOP_VISIBLE
            }
            UIState.TOP_VISIBLE -> {
                // 从顶部测量工具栏 → 底部按钮
                hideMeasurementToolbar()
                showBottomButtons()  // 这里需要显示底部按钮
                currentUIState = UIState.BOTTOM_VISIBLE
            }
            UIState.ALL_HIDDEN -> {
                // 从全部隐藏 → 底部按钮（备用状态）
                showBottomButtons()
                currentUIState = UIState.BOTTOM_VISIBLE
            }
        }
    }

    private fun showBottomButtons() {
        binding.buttonPanel.visibility = View.VISIBLE
    }

    private fun hideBottomButtons() {
        binding.buttonPanel.visibility = View.GONE
    }

    private fun showMeasurementToolbar() {
        if (measurementDialogFragment?.isAdded == true) return

        val args = Bundle().apply {
            putInt("anchor_x", 0)
            putInt("anchor_y", 0) 
            putInt("anchor_width", 0) // 设置为0表示顶部居中模式
        }

        measurementDialogFragment = TpMeasurementDialogFragment().apply {
            arguments = args
        }

        try {
            measurementDialogFragment?.show(parentFragmentManager, "MeasurementToolbar")
        } catch (e: IllegalStateException) {
            android.util.Log.e("ImageDialog", "Show measurement toolbar failed: ${e.message}")
        }
    }

    private fun hideMeasurementToolbar() {
        measurementDialogFragment?.dismiss()
        measurementDialogFragment = null
    }

    /**
     * � 强制工具栏状态 - 确保测量工具栏显示并锁定
     */
    private fun forceToolbarState() {
        // 强制切换到工具栏显示状态
        hideBottomButtons()
        showMeasurementToolbar()
        currentUIState = UIState.TOP_VISIBLE
        Log.d("ImageDialog", "🔒 Forced UI state to TOP_VISIBLE and locked for measurement")
    }

    /**
     * � 禁用单击监听器 - 测量模式下防止UI状态切换
     */
    private fun disableSingleTapListener() {
        binding.imageView.setOnSingleTapListener(null)
        Log.d("ImageDialog", "🚫 Single tap listener disabled for measurement mode")
    }

    /**
     * ✅ 启用单击监听器 - 恢复正常UI交互
     */
    private fun enableSingleTapListener() {
        binding.imageView.setOnSingleTapListener {
            if (!isMeasuringAngle) {
                cycleUIState()
            } else {
                Log.d("ImageDialog", "🔒 UI state locked during measurement mode")
            }
        }
        Log.d("ImageDialog", "✅ Single tap listener enabled for normal mode")
    }

    /**
     * �🛡️ 保护测量模式 - 添加测量模式专用保护机制
     */
    private fun protectMeasurementMode() {
        // 确保测量状态标记正确设置
        isMeasuringAngle = true

        // 🚫 禁用单击监听器，防止UI状态切换
        disableSingleTapListener()

        Log.d("ImageDialog", "🛡️ Measurement mode protection activated - UI completely locked")
    }

    /**
     * �🚀 初始化测量功能 - 使用封装的MeasurementManager
     */
    private fun initializeMeasurement() {
        // 创建MeasurementManager实例
        measurementManager = MeasurementManager(
            context = requireContext(),
            imageView = binding.imageView,
            overlayView = binding.measurementOverlay,
            statusTextView = null // 可以传入状态显示TextView
        )

        Log.d("ImageDialog", "🚀 MeasurementManager initialized")
    }

    /**
     * 🎯 开始角度测量 - 极简API调用，确保工具栏保持显示
     */
    fun startAngleMeasurement() {
        if (isMeasuringAngle) return

        Log.d("ImageDialog", "🎯 Starting angle measurement...")

        // 🔒 强制锁定UI状态到工具栏显示
        forceToolbarState()

        // 🛡️ 添加测量模式保护
        protectMeasurementMode()

        // 确保有位图数据
        if (currentBitmap == null) {
            updateCurrentBitmap()
        }

        // 如果还是没有位图，创建一个默认的
        val bitmap = currentBitmap ?: run {
            Log.w("ImageDialog", "⚠️ No bitmap available, creating default")
            Bitmap.createBitmap(1000, 1000, Bitmap.Config.ARGB_8888)
        }

        try {
            // 🚀 初始化MeasurementManager
            measurementManager.initialize(bitmap)

            // 🎯 开始角度测量 - 一行代码搞定！
            if (measurementManager.startAngleMeasurement()) {
                Log.d("ImageDialog", "✅ Angle measurement started successfully - toolbar locked")
            } else {
                Log.e("ImageDialog", "❌ Failed to start angle measurement")
                // 如果启动失败，重置测量状态
                isMeasuringAngle = false
            }

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to start angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * ⏹️ 停止角度测量 - 极简API调用，解锁UI状态
     */
    fun stopAngleMeasurement() {
        if (!isMeasuringAngle) return

        Log.d("ImageDialog", "⏹️ Stopping angle measurement...")

        try {
            // ⏹️ 停止角度测量 - 一行代码搞定！
            measurementManager.stopAngleMeasurement()
            isMeasuringAngle = false

            // ✅ 恢复单击监听器，解锁UI交互
            enableSingleTapListener()

            // 🔓 解锁UI状态，恢复正常交互
            Log.d("ImageDialog", "🔓 UI state unlocked - measurement stopped")

            Log.d("ImageDialog", "✅ Angle measurement stopped successfully")

        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Failed to stop angle measurement: ${e.message}")
            e.printStackTrace()
        }
    }

    // updateAngleMeasurementOverlay方法已被MeasurementManager内部处理，不再需要

    /**
     * 🧹 清理测量资源
     */
    override fun onDestroyView() {
        super.onDestroyView()

        try {
            if (::measurementManager.isInitialized) {
                measurementManager.cleanup()
            }
            Log.d("ImageDialog", "🧹 Measurement resources cleaned up")
        } catch (e: Exception) {
            Log.e("ImageDialog", "❌ Error cleaning up measurement resources: ${e.message}")
        }
    }

    /**
     * 🖼️ 加载图片时更新位图引用
     */
    private fun updateCurrentBitmap() {
        // 从ImageView的drawable获取位图
        val drawable = binding.imageView.drawable
        if (drawable != null) {
            try {
                // 创建位图副本用于测量
                val bitmap = Bitmap.createBitmap(
                    drawable.intrinsicWidth,
                    drawable.intrinsicHeight,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = android.graphics.Canvas(bitmap)
                drawable.setBounds(0, 0, canvas.width, canvas.height)
                drawable.draw(canvas)

                currentBitmap = bitmap
                Log.d("ImageDialog", "🖼️ Updated current bitmap: ${bitmap.width}x${bitmap.height}")
            } catch (e: Exception) {
                Log.e("ImageDialog", "❌ Failed to create bitmap from drawable: ${e.message}")
                // 创建一个默认的小位图作为备用
                currentBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
            }
        } else {
            Log.w("ImageDialog", "⚠️ No drawable available for bitmap creation")
            // 创建一个默认的小位图作为备用
            currentBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        hideMeasurementToolbar()

        // 清理测量资源
        if (isMeasuringAngle) {
            stopAngleMeasurement()
        }
    }
}
