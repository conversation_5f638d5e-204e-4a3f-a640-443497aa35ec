package com.touptek.measurerealize

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.ViewConfiguration
import android.view.animation.DecelerateInterpolator
import androidx.appcompat.widget.AppCompatImageView
import com.touptek.xcamview.BuildConfig
import kotlin.math.*

@SuppressLint("ClickableViewAccessibility")
class TpImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    // 缩放相关 - 提高灵敏度
    private var currentScale = 1f
    private var baseScale = 1f
    private var dynamicMinScale = 0.3f
    private val MAX_SCALE = 5.0f
    private val SCALE_SENSITIVITY = 3.0f  // 从 2.0f 提高到 3.0f，增加灵敏度
    
    // 缩放优化参数
    private val MIN_SCALE_SPAN = 10f  // 从默认值降低到10像素，更容易触发
    private val CUSTOM_SCALE_THRESHOLD = 3f  // 自定义缩放触发阈值

    // 矩阵和手势
    private val matrix = Matrix()
    private val savedMatrix = Matrix()
    private lateinit var scaleDetector: ScaleGestureDetector
    private lateinit var gestureDetector: GestureDetector
    
    // 状态标志
    private var isInitialized = false
    private var isScaling = false
    private var isCustomScaling = false // 自定义缩放状态
    private var isZoomEnabled = true
    
    // 视图和图片尺寸
    private var viewWidth = 0
    private var viewHeight = 0
    private var imageWidth = 0
    private var imageHeight = 0
    
    // 动画
    private var scaleAnimator: ValueAnimator? = null
    private var flingAnimator: ValueAnimator? = null

    // 自定义缩放检测变量
    private var lastDistance = -1f
    private var lastFocusX = 0f
    private var lastFocusY = 0f
    
    // 触摸相关
    private val touchSlop: Int by lazy {
        ViewConfiguration.get(context).scaledTouchSlop
    }

    // 添加单击监听器
    private var onSingleTapListener: (() -> Unit)? = null

    // 添加Matrix变化监听器
    private var matrixChangeListener: (() -> Unit)? = null

    init {
        scaleType = ScaleType.MATRIX
        setupGestureDetectors()
        setupTouchListener()
        
        // 配置缩放检测器的敏感度
        configureScaleDetectorSensitivity()
    }

    @SuppressLint("SoonBlockedPrivateApi")
    private fun configureScaleDetectorSensitivity() {
        try {
            // 使用反射降低最小跨度要求，让手指更容易触发缩放
            val field = ScaleGestureDetector::class.java.getDeclaredField("mMinSpan")
            field.isAccessible = true
            field.setInt(scaleDetector, MIN_SCALE_SPAN.toInt())
            Log.d("TpImageView", "✅ Scale sensitivity configured: minSpan=${MIN_SCALE_SPAN.toInt()}")
        } catch (e: Exception) {
            Log.w("TpImageView", "⚠️ Cannot configure scale sensitivity: ${e.message}")
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h
        updateMatrix()
    }

    override fun setImageBitmap(bm: Bitmap?) {
        super.setImageBitmap(bm)
        updateBitmapDimensions(bm)
        post {
            if (viewWidth > 0 && viewHeight > 0 && imageWidth > 0 && imageHeight > 0) {
                updateMatrix()
            }
        }
    }

    override fun setImageDrawable(drawable: Drawable?) {
        super.setImageDrawable(drawable)
        captureDrawableDimensions()
        post {
            if (viewWidth > 0 && viewHeight > 0 && imageWidth > 0 && imageHeight > 0) {
                updateMatrix()
            }
        }
    }

    private fun updateBitmapDimensions(bitmap: Bitmap?) {
        if (bitmap != null) {
            imageWidth = bitmap.width
            imageHeight = bitmap.height
        }
    }

    private fun captureDrawableDimensions() {
        drawable?.let { d ->
            imageWidth = d.intrinsicWidth
            imageHeight = d.intrinsicHeight
        }
    }

    private fun setupGestureDetectors() {
        scaleDetector = ScaleGestureDetector(context, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
                Log.d("TpImageView", "🔍 Scale begin - span: ${detector.currentSpan}")
                isScaling = true
                return true
            }

            override fun onScale(detector: ScaleGestureDetector): Boolean {
                if (!isInitialized) {
                    Log.w("TpImageView", "⚠️ Scale attempted but not initialized")
                    return false
                }

                val scaleFactor = detector.scaleFactor
                Log.d("TpImageView", "🔍 Raw scale factor: $scaleFactor")
                
                // 增强缩放敏感度 - 让小幅度手势也能产生明显效果
                val enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY
                val newScale = (currentScale * enhancedFactor).coerceIn(dynamicMinScale, MAX_SCALE)

                if (newScale != currentScale) {
                    val actualFactor = newScale / currentScale
                    currentScale = newScale
                    matrix.postScale(actualFactor, actualFactor, detector.focusX, detector.focusY)
                    updateImageMatrix(matrix)
                    Log.d("TpImageView", "✅ Scale applied: $currentScale (factor: $actualFactor)")
                }
                return true
            }

            override fun onScaleEnd(detector: ScaleGestureDetector) {
                isScaling = false
                // 🔄 缩放结束后同步状态
                syncCurrentScale()
                Log.d("TpImageView", "🔍 Scale end - synced scale: $currentScale")
                instantCheckBounds()
            }
        })

        // 配置缩放检测器敏感度
        configureScaleDetectorSensitivity()

        gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                Log.d("TpImageView", "🎯 Single tap confirmed")
                onSingleTapListener?.invoke()
                return true
            }

            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                if (!isInitialized || isScaling) return false

                if (isAtMinimumScale()) {
                    Log.d("TpImageView", "🚫 Dragging disabled at minimum scale")
                    return false
                }

                matrix.postTranslate(-distanceX, -distanceY)
                updateImageMatrix(matrix)
                return true
            }

            override fun onFling(
                e1: MotionEvent?,
                e2: MotionEvent,
                velocityX: Float,
                velocityY: Float
            ): Boolean {
                if (!isInitialized || isScaling || isAtMinimumScale()) return false
                startFlingAnimation(velocityX, velocityY)
                return true
            }

            override fun onDoubleTap(e: MotionEvent): Boolean {
                if (!isInitialized) return false

                // 🔄 获取真实的缩放状态
                val realScale = getCurrentScaleFactor()
                syncCurrentScale() // 同步状态

                Log.d("TpImageView", "🎯 Double tap detected - realScale=$realScale, baseScale=$baseScale")

                val targetScale = if (realScale > baseScale * 1.5f) {
                    baseScale // 缩小到适配大小
                } else {
                    min(MAX_SCALE, baseScale * 3f) // 放大到3倍
                }

                Log.d("TpImageView", "🎯 Double tap scaling: $realScale -> $targetScale")
                instantScaleTo(targetScale, e.x, e.y)
                return true
            }

            override fun onLongPress(e: MotionEvent) {
                Log.d("TpImageView", "� Long press detected - ignoring")
            }
        })
    }

    private fun setupTouchListener() {
        setOnTouchListener { _, event ->
            var handled = false

            Log.d("TpImageView", "🎯 Touch event received, zoom enabled: $isZoomEnabled, measurement handler: ${measurementTouchHandler != null}")

            // 如果有测量处理器且是单点触摸，优先处理测量
            if (measurementTouchHandler != null && event.pointerCount == 1) {
                val viewWidth = width
                val viewHeight = height
                if (viewWidth > 0 && viewHeight > 0) {
                    if (measurementTouchHandler!!.invoke(event, viewWidth, viewHeight)) {
                        handled = true
                        performClick()
                        return@setOnTouchListener true
                    }
                }
            }

            if (isZoomEnabled) {
                // 自定义小距离缩放检测
                handleCustomScale(event)

                // 优先处理缩放手势
                if (scaleDetector.onTouchEvent(event)) {
                    handled = true
                }

                // 只有在非缩放状态下才处理其他手势
                if (!scaleDetector.isInProgress && !isCustomScaling) {
                    if (gestureDetector.onTouchEvent(event)) {
                        handled = true
                    }
                }

                when (event.actionMasked) {
                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        if (!isScaling && !isCustomScaling) {
                            instantCheckBounds()
                        }
                        // 🔄 触摸结束时同步状态
                        syncCurrentScale()
                        isCustomScaling = false
                        lastDistance = -1f
                        Log.d("TpImageView", "👆 Touch ended, final scale: $currentScale")
                    }
                }
            }

            // 重要：如果事件被处理了，就不要调用 performClick()
            if (!handled) {
                performClick()
            }

            // 总是返回 true 来消费事件，避免传递给父视图
            true
        }
    }

    private fun updateMatrix() {
        if (viewWidth <= 0 || viewHeight <= 0 || imageWidth <= 0 || imageHeight <= 0) {
            Log.w("TpImageView", "❌ Invalid dimensions: view=$viewWidth x $viewHeight, image=$imageWidth x $imageHeight")
            return
        }

        baseScale = min(
            viewWidth.toFloat() / imageWidth,
            viewHeight.toFloat() / imageHeight
        )

        dynamicMinScale = baseScale
        currentScale = baseScale

        matrix.reset()
        matrix.postScale(baseScale, baseScale)
        matrix.postTranslate(
            (viewWidth - imageWidth * baseScale) / 2f,
            (viewHeight - imageHeight * baseScale) / 2f
        )

        updateImageMatrix(matrix)
        isInitialized = true
        
        Log.d("TpImageView", "✅ Matrix initialized: baseScale=$baseScale, isInitialized=$isInitialized")
        Log.d("TpImageView", "📐 View: ${viewWidth}x${viewHeight}, Image: ${imageWidth}x${imageHeight}")
    }

    /**
     * 🔧 获取当前真实缩放级别 - 从矩阵读取准确值
     */
    private fun getCurrentScaleFactor(): Float {
        val values = FloatArray(9)
        matrix.getValues(values)
        val scaleX = values[Matrix.MSCALE_X]
        val scaleY = values[Matrix.MSCALE_Y]
        return sqrt(scaleX * scaleX + scaleY * scaleY) / sqrt(2f)
    }

    /**
     * 🔄 同步currentScale变量与矩阵状态
     */
    private fun syncCurrentScale() {
        val realScale = getCurrentScaleFactor()
        if (abs(currentScale - realScale) > 0.01f) {
            Log.d("TpImageView", "🔄 Scale sync: currentScale=$currentScale -> realScale=$realScale")
            currentScale = realScale
        }
    }

    /**
     * 🔧 检查是否处于最小缩放状态 - 使用真实矩阵值
     */
    private fun isAtMinimumScale(): Boolean {
        val realScale = getCurrentScaleFactor()
        return realScale <= dynamicMinScale * 1.01f
    }

    private fun instantCheckBounds() {
        if (!isInitialized) return

        val rect = getDisplayRect()
        val deltaX = when {
            rect.width() <= viewWidth -> (viewWidth - rect.width()) / 2f - rect.left
            rect.left > 0 -> -rect.left
            rect.right < viewWidth -> viewWidth - rect.right
            else -> 0f
        }

        val deltaY = when {
            rect.height() <= viewHeight -> (viewHeight - rect.height()) / 2f - rect.top
            rect.top > 0 -> -rect.top
            rect.bottom < viewHeight -> viewHeight - rect.bottom
            else -> 0f
        }

        if (abs(deltaX) > 1f || abs(deltaY) > 1f) {
            animateTranslate(deltaX, deltaY)
        } else {
            // 🔄 边界检查完成后同步缩放状态
            syncCurrentScale()
        }
    }

    private fun animateTranslate(deltaX: Float, deltaY: Float) {
        cancelAnimations()
        savedMatrix.set(matrix)

        ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 200L
            interpolator = DecelerateInterpolator()
            addUpdateListener { animator ->
                val progress = animator.animatedValue as Float
                matrix.set(savedMatrix)
                matrix.postTranslate(deltaX * progress, deltaY * progress)
                updateImageMatrix(matrix)
            }
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 🔄 动画结束后同步缩放状态
                    syncCurrentScale()
                    Log.d("TpImageView", "🎬 Translate animation ended, scale synced: $currentScale")
                }
            })
            start()
        }
    }

    private fun startFlingAnimation(velocityX: Float, velocityY: Float) {
        cancelAnimations()

        val startVelX = velocityX * 0.3f
        val startVelY = velocityY * 0.3f

        flingAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 800L
            interpolator = DecelerateInterpolator()

            addUpdateListener { animator ->
                val progress = animator.animatedValue as Float
                val currentVelX = startVelX * (1f - progress)
                val currentVelY = startVelY * (1f - progress)

                matrix.postTranslate(currentVelX * 0.016f, currentVelY * 0.016f)
                updateImageMatrix(matrix)
            }
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 🔄 惯性滑动结束后同步状态并检查边界
                    syncCurrentScale()
                    instantCheckBounds()
                    Log.d("TpImageView", "🎬 Fling animation ended, scale synced: $currentScale")
                }
            })
            start()
        }
    }

    private fun instantScaleTo(targetScale: Float, focusX: Float, focusY: Float) {
        if (!isInitialized) return

        cancelAnimations()
        val scaleFactor = targetScale / currentScale
        currentScale = targetScale

        matrix.postScale(scaleFactor, scaleFactor, focusX, focusY)
        updateImageMatrix(matrix)
        instantCheckBounds()

        // 🔄 缩放完成后验证状态一致性
        syncCurrentScale()
        Log.d("TpImageView", "🎯 Instant scale completed: target=$targetScale, final=$currentScale")
    }

    private fun getDisplayRect(): RectF {
        val rect = RectF(0f, 0f, imageWidth.toFloat(), imageHeight.toFloat())
        matrix.mapRect(rect)
        return rect
    }

    private fun updateImageMatrix(newMatrix: Matrix) {
        imageMatrix = newMatrix

        // 🔍 验证状态一致性（仅在调试模式下）
        if (BuildConfig.DEBUG && isInitialized) {
            val realScale = getCurrentScaleFactor()
            if (abs(currentScale - realScale) > 0.05f) {
                Log.w("TpImageView", "⚠️ Scale inconsistency detected: currentScale=$currentScale, realScale=$realScale")
            }
        }

        // 通知Matrix变化监听器
        matrixChangeListener?.invoke()
    }

    private fun cancelAnimations() {
        scaleAnimator?.cancel()
        flingAnimator?.cancel()
    }

    override fun performClick(): Boolean {
        super.performClick()
        return true
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cancelAnimations()
    }

    // 公共API
    fun getCurrentScale(): Float = currentScale
    fun getBaseScale(): Float = baseScale
    fun setZoomEnabled(enabled: Boolean) {
        isZoomEnabled = enabled
        if (!enabled) {
            cancelAnimations()
        }
    }

    fun setOnSingleTapListener(listener: (() -> Unit)?) {
        onSingleTapListener = listener
    }

    fun setMatrixChangeListener(listener: (() -> Unit)?) {
        matrixChangeListener = listener
    }

    // 测量触摸处理器
    private var measurementTouchHandler: ((MotionEvent, Int, Int) -> Boolean)? = null

    /**
     * 🎯 设置测量触摸处理器 - 支持缩放+测量的混合模式
     */
    fun setMeasurementTouchHandler(handler: ((MotionEvent, Int, Int) -> Boolean)?) {
        measurementTouchHandler = handler
        Log.d("TpImageView", "🎯 Measurement touch handler set: ${handler != null}")
    }

    /**
     * 🔄 恢复原始触摸监听器 - 清除测量处理器
     */
    fun restoreTouchListener() {
        measurementTouchHandler = null
        setupTouchListener() // 重新设置原始触摸监听器
        Log.d("TpImageView", "🔄 Touch listener restored to original state")
    }

    // 自定义小距离缩放检测
    private fun handleCustomScale(event: MotionEvent) {
        when (event.actionMasked) {
            MotionEvent.ACTION_POINTER_DOWN -> {
                if (event.pointerCount == 2) {
                    lastDistance = getDistance(event)
                    lastFocusX = (event.getX(0) + event.getX(1)) / 2f
                    lastFocusY = (event.getY(0) + event.getY(1)) / 2f
                    Log.d("TpImageView", "🔍 Custom scale init - distance: $lastDistance")
                }
            }
            MotionEvent.ACTION_MOVE -> {
                if (event.pointerCount == 2 && lastDistance > 0) {
                    val currentDistance = getDistance(event)
                    val currentFocusX = (event.getX(0) + event.getX(1)) / 2f
                    val currentFocusY = (event.getY(0) + event.getY(1)) / 2f

                    // 检测小距离变化（使用更小的阈值）
                    val distanceChange = abs(currentDistance - lastDistance)
                    if (distanceChange >= CUSTOM_SCALE_THRESHOLD && !scaleDetector.isInProgress) {
                        isCustomScaling = true

                        val scaleFactor = currentDistance / lastDistance
                        val enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY
                        val newScale = (currentScale * enhancedFactor).coerceIn(dynamicMinScale, MAX_SCALE)

                        if (newScale != currentScale) {
                            val actualFactor = newScale / currentScale
                            currentScale = newScale

                            matrix.postScale(actualFactor, actualFactor, currentFocusX, currentFocusY)
                            updateImageMatrix(matrix)
                            // 🔄 自定义缩放后验证状态
                            syncCurrentScale()
                            Log.d("TpImageView", "✅ Custom scale applied: $currentScale (factor: $actualFactor)")
                        }

                        lastDistance = currentDistance
                        lastFocusX = currentFocusX
                        lastFocusY = currentFocusY
                    }
                }
            }
        }
    }

    // 计算两指间距离
    private fun getDistance(event: MotionEvent): Float {
        val dx = event.getX(0) - event.getX(1)
        val dy = event.getY(0) - event.getY(1)
        return sqrt(dx * dx + dy * dy)
    }
}





