package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎨 专业级角度测量助手 - 核心控制器
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u0000 82\u00020\u0001:\u00018B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u001c\u001a\u00020\nJ\u0006\u0010\u001d\u001a\u00020\u0014J\u001e\u0010\u001e\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0012\u0004\u0012\u00020\u00060\u001f2\u0006\u0010 \u001a\u00020!H\u0002J\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020$0#J\u0006\u0010%\u001a\u00020\u0006J\u001e\u0010&\u001a\u00020\n2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020\u00062\u0006\u0010*\u001a\u00020\u0006J\u001e\u0010+\u001a\u00020\u00142\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010,\u001a\u00020\u0018J\u000e\u0010-\u001a\u00020\n2\u0006\u0010 \u001a\u00020!J\b\u0010.\u001a\u00020\u0014H\u0002J\u0006\u0010/\u001a\u00020\u0014J\u0006\u00100\u001a\u00020\u0014J\u0006\u00101\u001a\u00020\u0014J\u0006\u00102\u001a\u00020\u0014J\u0014\u00103\u001a\u00020\u00142\f\u00104\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013J\b\u00105\u001a\u00020\u0014H\u0002J\u0006\u00106\u001a\u000207R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\u0014\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00069"}, d2 = {"Lcom/touptek/measurerealize/utils/AngleMeasureHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/AngleMeasurement;", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isCreatingNew", "", "isDraggingPoint", "isDraggingText", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "Ljava/util/ArrayList;", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "textView", "Landroid/widget/TextView;", "checkAndRecoverData", "clearAllMeasurements", "findTouchedPoint", "Lkotlin/Pair;", "touchPoint", "Landroid/graphics/PointF;", "getAllMeasurementData", "", "Lcom/touptek/measurerealize/utils/AngleMeasurementData;", "getMeasurementCount", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "init", "bitmap", "isTouchingMeasurementPoint", "notifyUpdate", "onScaleChanged", "pauseMeasurement", "resetTouchState", "resumeMeasurement", "setMeasurementUpdateCallback", "callback", "setupTouchListener", "startNewMeasurement", "", "Companion", "app_debug"})
public final class AngleMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.AngleMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "AngleMeasureHelper";
    private static final float POINT_RADIUS = 14.0F;
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float HIGHLIGHT_RADIUS = 18.0F;
    private static final float DRAG_RADIUS = 22.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final int COLOR_VERTEX = 0;
    private static final int COLOR_POINT1 = 0;
    private static final int COLOR_POINT2 = 0;
    private static final int COLOR_LINE = 0;
    private static final int COLOR_ARC = 0;
    private com.touptek.measurerealize.TpImageView imageView;
    private android.widget.TextView textView;
    private android.graphics.Bitmap originalBitmap;
    private final java.util.ArrayList<com.touptek.measurerealize.utils.AngleMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.AngleMeasurement activeMeasurement;
    private com.touptek.measurerealize.utils.AngleMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private boolean isDraggingText = false;
    private int draggedPointIndex = -1;
    private boolean isCreatingNew = false;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public AngleMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化测量助手 - 专业级初始化流程
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.widget.TextView textView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🎯 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🎨 开始新的角度测量 - 在屏幕中心生成可拖动的角度
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🎨 设置触摸监听器 - 专业级手势识别
     * 注意：不直接设置OnTouchListener，而是通过覆盖层处理触摸事件
     */
    private final void setupTouchListener() {
    }
    
    /**
     * 🎯 处理触摸事件 - 完全复制yolo_demo签名
     */
    @kotlin.Suppress(names = {"UNUSED_PARAMETER"})
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🔍 检测触摸点是否在测量点位上 - 公共方法供MeasurementOverlay使用
     */
    public final boolean isTouchingMeasurementPoint(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔍 查找被触摸的点
     */
    private final kotlin.Pair<com.touptek.measurerealize.utils.AngleMeasurement, java.lang.Integer> findTouchedPoint(android.graphics.PointF touchPoint) {
        return null;
    }
    
    /**
     * 🎨 获取所有测量数据用于覆盖层显示 - 使用视图坐标
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.AngleMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🔄 缩放变化时同步坐标 - 专业绘图应用体验
     */
    public final void onScaleChanged() {
    }
    
    /**
     * ⏸️ 暂停测量 - 保留所有数据和状态
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * ▶️ 恢复测量 - 从暂停状态恢复到可编辑状态
     */
    public final void resumeMeasurement() {
    }
    
    /**
     * 🧹 清除所有测量
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🔄 重置触摸状态 - 用于解决触摸状态不一致问题
     */
    public final void resetTouchState() {
    }
    
    /**
     * � 检测并修复数据丢失问题
     */
    public final boolean checkAndRecoverData() {
        return false;
    }
    
    /**
     * �📊 通知更新
     */
    private final void notifyUpdate() {
    }
    
    /**
     * 📊 获取测量数量（调试用）
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/touptek/measurerealize/utils/AngleMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "COLOR_ARC", "", "COLOR_LINE", "COLOR_POINT1", "COLOR_POINT2", "COLOR_VERTEX", "DRAG_RADIUS", "HIGHLIGHT_RADIUS", "LONG_PRESS_DURATION", "", "POINT_RADIUS", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}